import {deleteAction, getAction, postAction, putAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";
import axios from 'axios';



// 进口合同信息列表
export const insertPlan = (params) =>window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.plan.insert, params)
export const updatePlan = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.importedCigarettes.plan.update}/${sid}`, params)
export const deletePlan = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.importedCigarettes.plan.delete}/${sids}`)
export const confirmPlan = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.importedCigarettes.plan.confirm}/${sid}`)
export const sendAudit = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.importedCigarettes.plan.sendAudit}/${sids}`)
export const invalidatePlan = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.importedCigarettes.plan.invalidate}/${sids}`)
export const checkPlanIdNotCancel = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.importedCigarettes.plan.checkPlanIdNotCancel}`,params)
export const copyVersion = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.importedCigarettes.plan.copyVersion}`,params)

export const insertPlanWithDetails = (data) => {
  return window.majesty.httpUtil.postAction(`${ycCsApi.importedCigarettes.plan.insertPlanWithDetails}`, data);
};
export const updatePlanWithDetails = (data) => {
  return window.majesty.httpUtil.postAction(`${ycCsApi.importedCigarettes.plan.updatePlanWithDetails}`, data);
};
export const insertPlanList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.planList.insert, params)

export const updatePlanList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.importedCigarettes.planList.update}/${sid}`, params)

export const deletePlanList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.importedCigarettes.planList.delete}/${sids}`)

// 校验单行表体数据
export const validateDetailData = (params) => window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.planList.validateDetailData, params)

/* 获取自定义配置信息 */
export const saveCustomWithDataId = (params)=>window.majesty.httpUtil.postAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/saveCustomWithDataId', params)
export const getCustomVaueByTypeAndDataId = (params) =>window.majesty.httpUtil.getAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/getCustomVaueByTypeAndDataId', params)
