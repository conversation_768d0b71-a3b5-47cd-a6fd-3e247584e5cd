import CsConstant from "@/api/CsConstant";
import {updatePlanWithDetails} from "@/api/importedCigarettes/plan/planApi";
import {
  addPushListInvoice, batchUpdateBoxList,
  checkNextModuleExistEffectiveData, deletePurchaseListBox, enableQuoList,
  generateIOrder, getOrderListBySid, getPurchaseListBySid,
  innerDecTotalUpdatePurchaseList
} from "@/api/cs_api_constant";
export default {


  /* 上传附件请求 */
  baseAttach: {
    insert: CsConstant.PREFIX_CS + 'v1/attached/insert',
    getAttechedList: CsConstant.PREFIX_CS + 'v1/attached/getAttechedList',
    getAttechedListByType: CsConstant.PREFIX_CS + 'v1/attached/getAttechedListByType',
    download: CsConstant.PREFIX_CS + 'v1/attached',
    delete: CsConstant.PREFIX_CS + 'v1/attached',
    // 根据sid获取文件信息
    getAttachFile: CsConstant.PREFIX_CS + 'v1/attached/getAttachFile',
  },


  /* 海关参数自定义信息url */
  baseInfoCustomerParams: {
    list: CsConstant.PREFIX_CS + 'v1/baseInfoCustomerParams/list',
    update: CsConstant.PREFIX_CS + 'v1/baseInfoCustomerParams',
    insert: CsConstant.PREFIX_CS + 'v1/baseInfoCustomerParams',
    delete: CsConstant.PREFIX_CS + 'v1/baseInfoCustomerParams',
  },


  /* 客户基础信息url */
  biClientInfo: {
    list: CsConstant.PREFIX_CS + 'v1/biClientInformation/list',
    update: CsConstant.PREFIX_CS + 'v1/biClientInformation',
    insert: CsConstant.PREFIX_CS + 'v1/biClientInformation',
    delete: CsConstant.PREFIX_CS + 'v1/biClientInformation',
    export: CsConstant.PREFIX_CS + 'v1/biClientInformation/export',
  },

  /* shipfrom 基础信息url */
  biShipfrom: {
    list: CsConstant.PREFIX_CS + 'v1/biShipfrom/list',
    update: CsConstant.PREFIX_CS + 'v1/biShipfrom',
    insert: CsConstant.PREFIX_CS + 'v1/biShipfrom',
    delete: CsConstant.PREFIX_CS + 'v1/biShipfrom',
    export: CsConstant.PREFIX_CS + 'v1/biShipfrom/export',
  },

  /*
  进口卷烟
   */
  importedCigarettes: {
    contract: {
      list: CsConstant.PREFIX_CS + 'v1/bizIContractHead/list',
      planList: CsConstant.PREFIX_CS + 'v1/bizIContractHead/planList',
      customsList: CsConstant.PREFIX_CS + 'v1/bizIContractHead/customsList',
      update: CsConstant.PREFIX_CS + 'v1/bizIContractHead',
      insert: CsConstant.PREFIX_CS + 'v1/bizIContractHead',
      delete: CsConstant.PREFIX_CS + 'v1/bizIContractHead',
      export: CsConstant.PREFIX_CS + 'v1/bizIContractHead/export',
      confirm: CsConstant.PREFIX_CS + 'v1/bizIContractHead/confirm',
      sendAudit: CsConstant.PREFIX_CS + 'v1/bizIContractHead/sendAudit',
      cancel: CsConstant.PREFIX_CS + 'v1/bizIContractHead/cancel',
      checkStatus: CsConstant.PREFIX_CS + 'v1/bizIContractHead/checkStatus',
      copy: CsConstant.PREFIX_CS + 'v1/bizIContractHead/copy',
      sellerList: CsConstant.PREFIX_CS + 'v1/bizIContractHead/sellerList',
    },
    contractList: {
      list: CsConstant.PREFIX_CS + 'v1/bizIContractList/list',
      update: CsConstant.PREFIX_CS + 'v1/bizIContractList',
      insert: CsConstant.PREFIX_CS + 'v1/bizIContractList',
      delete: CsConstant.PREFIX_CS + 'v1/bizIContractList',
      export: CsConstant.PREFIX_CS + 'v1/bizIContractList/export',
      getContractTotal: CsConstant.PREFIX_CS + 'v1/bizIContractList/getContractTotal',
    },
    plan : {
      list: CsConstant.PREFIX_CS + '/v1/bizIplan/list',
      insert: CsConstant.PREFIX_CS + '/v1/bizIplan',
      delete: CsConstant.PREFIX_CS + '/v1/bizIplan',
      update: CsConstant.PREFIX_CS + '/v1/bizIplan',
      export: CsConstant.PREFIX_CS + '/v1/bizIplan/export',
      confirm: CsConstant.PREFIX_CS + '/v1/bizIplan/confirm',
      sendAudit: CsConstant.PREFIX_CS + '/v1/bizIplan/sendApproval',
      invalidate: CsConstant.PREFIX_CS + '/v1/bizIplan/invalidate',
      checkPlanIdNotCancel: CsConstant.PREFIX_CS + '/v1/bizIplan/checkPlanIdNotCancel',
      copyVersion: CsConstant.PREFIX_CS + '/v1/bizIplan/copyVersion',
      insertPlanWithDetails: CsConstant.PREFIX_CS + '/v1/bizIplan/insertPlanWithDetails',
      updatePlanWithDetails: CsConstant.PREFIX_CS + '/v1/bizIplan/updatePlanWithDetails',
    },
    planList : {
      list: CsConstant.PREFIX_CS + '/v1/bizIPlanList/list',
      insert: CsConstant.PREFIX_CS + '/v1/bizIPlanList',
      delete: CsConstant.PREFIX_CS + '/v1/bizIPlanList',
      update: CsConstant.PREFIX_CS + '/v1/bizIPlanList',
      export:CsConstant.PREFIX_CS + '/v1/bizIPlanList/export',
      validateDetailData: CsConstant.PREFIX_CS + '/v1/bizIPlanList/validateDetailData'
    }


  },
  /**
   * 进口辅料
   */
  auxiliaryMaterials:{
    bizIAuxMatForContractHead : {
      list: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/list',
      planList: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/planList',
      customsList: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/customsList',
      update: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead',
      insert: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead',
      delete: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead',
      export: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/export',
      confirm: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/confirm',
      sendAudit: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/sendAudit',
      cancel: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/cancel',
      checkStatus: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/checkStatus',
      copy: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractHead/copy',
    },
    bizIAuxMatForContractList : {
      list: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList/list',
      update: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList',
      insert: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList',
      delete: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList',
      export: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList/export',
      getContractTotal: CsConstant.PREFIX_CS + 'v1/bizIAuxmatForContractList/getContractTotal',
    },
    contract: {
      list: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/list',
      planList: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/planList',
      sellerList: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/sellerList',
      update: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead',
      insert: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead',
      delete: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead',
      export: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/export',
      confirm: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/confirm',
      sendAudit: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/sendAudit',
      cancel: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/cancel',
      checkStatus: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/checkStatus',
      copy: CsConstant.PREFIX_CS + 'v1/bizForeignContractHead/copy',
    },
    contractList: {
      list: CsConstant.PREFIX_CS + 'v1/bizForeignContractList/list',
      update: CsConstant.PREFIX_CS + 'v1/bizForeignContractList',
      insert: CsConstant.PREFIX_CS + 'v1/bizForeignContractList',
      delete: CsConstant.PREFIX_CS + 'v1/bizForeignContractList',
      export: CsConstant.PREFIX_CS + 'v1/bizForeignContractList/export',
      getContractTotal: CsConstant.PREFIX_CS + 'v1/bizForeignContractList/getContractTotal',
    }
  },
  /*进口费用*/
  costI:{
    list: CsConstant.PREFIX_CS + 'v1/expenseIHead/list',
    update: CsConstant.PREFIX_CS + 'v1/expenseIHead',
    insert: CsConstant.PREFIX_CS + 'v1/expenseIHead',
    delete: CsConstant.PREFIX_CS + 'v1/expenseIHead',
    chargeback: CsConstant.PREFIX_CS + 'v1/expenseIHead/chargeback',
    affirm: CsConstant.PREFIX_CS + 'v1/expenseIHead/affirm',
    cancellation: CsConstant.PREFIX_CS + 'v1/expenseIHead/cancellation',
    copy: CsConstant.PREFIX_CS + 'v1/expenseIHead/copy',
    export: CsConstant.PREFIX_CS + 'v1/expenseIHead/export',
    Ilist:{
      list: CsConstant.PREFIX_CS + 'v1/expenseIList/list',
      update: CsConstant.PREFIX_CS + 'v1/expenseIList',
      delete: CsConstant.PREFIX_CS + 'v1/expenseIList',
      export: CsConstant.PREFIX_CS + 'v1/expenseIList/export',
      getSumDataCost: CsConstant.PREFIX_CS + 'v1/expenseIList/getSumDataByInvoiceSummary',
      IContract: {
        selectCostType: CsConstant.PREFIX_CS + 'v1/expenseIList/selectCostType',
       list: CsConstant.PREFIX_CS + 'v1/expenseIList/listContract',
       listPayment: CsConstant.PREFIX_CS + 'v1/expenseIList/listContractPayment',
       insert: CsConstant.PREFIX_CS + 'v1/expenseIList/insertContractDetail',
      },
      shippingOrder: {
        list: CsConstant.PREFIX_CS + 'v1/expenseIList/listShippingOrder',
        listPayment: CsConstant.PREFIX_CS + 'v1/expenseIList/listShippingOrderPayment',
        insert: CsConstant.PREFIX_CS + 'v1/expenseIList/insertShippingOrder',
        upload: CsConstant.PREFIX_CS + 'v1/expenseIList/upload',
      }
    }
  },
  /* 客商信息 基础信息url */
    bizMerchant : {
      list: CsConstant.PREFIX_CS + `/v1/bizMerchant/list`,
      insert: CsConstant.PREFIX_CS + `/v1/bizMerchant`,
      delete: CsConstant.PREFIX_CS + `/v1/bizMerchant`,
      update: CsConstant.PREFIX_CS + `/v1/bizMerchant`,
      export: CsConstant.PREFIX_CS + `/v1/bizMerchant/export`,
      getMerchantCode: CsConstant.PREFIX_CS + `/v1/bizMerchant/getMerchantCode`
    },
  bizMaterialInformation : {
    list: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/list`,
    insert: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation`,
    delete: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation`,
    update: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation`,
    getMerchantCodeValue: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/getMerchantCodeValue`,
    export: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/export`,
    cancel: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/cancel`,
    matForPlan: CsConstant.PREFIX_CS +`/v1/bizMaterialInformation/matForPlan`,
  },
  /* 企业参数 */
  params:{
    storehouse : {
      list: CsConstant.PREFIX_CS + `/v1/storehouse/list`,
      insert: CsConstant.PREFIX_CS + `/v1/storehouse`,
      delete: CsConstant.PREFIX_CS + `/v1/storehouse`,
      update: CsConstant.PREFIX_CS + `/v1/storehouse`,
      export: CsConstant.PREFIX_CS + `/v1/storehouse/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/storehouse/getNextCode`,
    },
    productType : {
      list: CsConstant.PREFIX_CS + `/v1/productType/list`,
      insert: CsConstant.PREFIX_CS + `/v1/productType`,
      delete: CsConstant.PREFIX_CS + `/v1/productType`,
      update: CsConstant.PREFIX_CS + `/v1/productType`,
      export: CsConstant.PREFIX_CS + `/v1/productType/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/productType/getNextCode`,
    },
    packageInfo : {
      list: CsConstant.PREFIX_CS + `/v1/packageInfo/list`,
      insert: CsConstant.PREFIX_CS + `/v1/packageInfo`,
      delete: CsConstant.PREFIX_CS + `/v1/packageInfo`,
      update: CsConstant.PREFIX_CS + `/v1/packageInfo`,
      export: CsConstant.PREFIX_CS + `/v1/packageInfo/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/packageInfo/getNextCode`,
    },
    costType : {
      list: CsConstant.PREFIX_CS + `/v1/costType/list`,
      insert: CsConstant.PREFIX_CS + `/v1/costType`,
      delete: CsConstant.PREFIX_CS + `/v1/costType`,
      update: CsConstant.PREFIX_CS + `/v1/costType`,
      export: CsConstant.PREFIX_CS + `/v1/costType/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/costType/getNextCode`,
    },
    rateTable : {
      list: CsConstant.PREFIX_CS + `/v1/rateTable/list`,
      insert: CsConstant.PREFIX_CS + `/v1/rateTable`,
      delete: CsConstant.PREFIX_CS + `/v1/rateTable`,
      update: CsConstant.PREFIX_CS + `/v1/rateTable`,
      export: CsConstant.PREFIX_CS + `/v1/rateTable/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/rateTable/getNextCode`,
    },
    enterpriseRate : {
      list: CsConstant.PREFIX_CS + `/v1/EnterpriseRate/list`,
      insert: CsConstant.PREFIX_CS + `/v1/EnterpriseRate`,
      delete: CsConstant.PREFIX_CS + `/v1/EnterpriseRate`,
      update: CsConstant.PREFIX_CS + `/v1/EnterpriseRate`,
      export: CsConstant.PREFIX_CS + `/v1/EnterpriseRate/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/EnterpriseRate/getNextCode`,
    },
    priceTerms: {
      list: CsConstant.PREFIX_CS + '/v1/priceTerms/list',
      listAll: CsConstant.PREFIX_CS + '/v1/priceTerms/listAll',
      insert: CsConstant.PREFIX_CS + '/v1/priceTerms',
      update: CsConstant.PREFIX_CS + '/v1/priceTerms',
      delete: CsConstant.PREFIX_CS + '/v1/priceTerms',
      export: CsConstant.PREFIX_CS + '/v1/priceTerms/export',
      getSno: CsConstant.PREFIX_CS + '/v1/priceTerms/nextParamCode',
    },
    city: {
      list: CsConstant.PREFIX_CS + '/v1/city/list',
      insert: CsConstant.PREFIX_CS + '/v1/city',
      update: CsConstant.PREFIX_CS + '/v1/city',
      delete: CsConstant.PREFIX_CS + '/v1/city',
      export: CsConstant.PREFIX_CS + '/v1/city/export',
      getSno: CsConstant.PREFIX_CS + '/v1/city/nextParamCode',
    },
    transCode: {
      list: CsConstant.PREFIX_CS + '/v1/transCode/list',
      insert: CsConstant.PREFIX_CS + '/v1/transCode',
      update: CsConstant.PREFIX_CS + '/v1/transCode',
      delete: CsConstant.PREFIX_CS + '/v1/transCode',
      export: CsConstant.PREFIX_CS + '/v1/transCode/export',
      copy: CsConstant.PREFIX_CS + '/v1/transCode/copy',


    },

    boxType : {
      list: CsConstant.PREFIX_CS + `/v1/boxType/list`,
      insert: CsConstant.PREFIX_CS + `/v1/boxType`,
      delete: CsConstant.PREFIX_CS + `/v1/boxType`,
      update: CsConstant.PREFIX_CS + `/v1/boxType`,
      export: CsConstant.PREFIX_CS + `/v1/boxType/export`,
      getSno: CsConstant.PREFIX_CS + `/v1/boxType/getNextCode`,
      getBoxTypeMap: CsConstant.PREFIX_CS + '/v1/boxType/getBoxTypeMap',
    },
  },
  payment:{
    notifyHead : {
      list: CsConstant.PREFIX_CS + `/v1/notifyHead/list`,
      insert: CsConstant.PREFIX_CS + `/v1/notifyHead`,
      delete: CsConstant.PREFIX_CS + `/v1/notifyHead`,
      update: CsConstant.PREFIX_CS + `/v1/notifyHead`,
      export: CsConstant.PREFIX_CS + `/v1/notifyHead/export`,
      getDocNo: CsConstant.PREFIX_CS + `/v1/notifyHead/getDocNo`,
      getRate: CsConstant.PREFIX_CS + `/v1/notifyHead/getRate`,
      getUserInfo: CsConstant.PREFIX_CS + `/v1/notifyHead/getUserInfo`,
      confirm: CsConstant.PREFIX_CS + `/v1/notifyHead/confirm`,
      cancel: CsConstant.PREFIX_CS + `/v1/notifyHead/cancel`,
      back: CsConstant.PREFIX_CS + `/v1/notifyHead/back`,
      copy: CsConstant.PREFIX_CS + `/v1/notifyHead/copy`,
      print: CsConstant.PREFIX_CS + `/v1/notifyHead/print`,

    },
    notifyList : {
      list: CsConstant.PREFIX_CS + `/v1/notifyList/list`,
      insert: CsConstant.PREFIX_CS + `/v1/notifyList`,
      insertOrder: CsConstant.PREFIX_CS + `/v1/notifyList/insertOrder`,
      delete: CsConstant.PREFIX_CS + `/v1/notifyList/delete`,
      update: CsConstant.PREFIX_CS + `/v1/notifyList`,
      export: CsConstant.PREFIX_CS + `/v1/notifyList/export`,

    },
    settlement : {
      list: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement/list`,
      insert: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement`,
      delete: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement`,
      update: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement`,
      confirm: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement/confirm`,
      back: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement/back`,
      invalidate: CsConstant.PREFIX_CS + '/v1/bizPaymentSettlement/invalidate',
      export: CsConstant.PREFIX_CS + `/v1/bizPaymentSettlement/export`
    }
  },




  /* 进口订单表头信息 */
  bizIOrderHead: {
    list: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIOrderHead',
    insert: CsConstant.PREFIX_CS + 'v1/bizIOrderHead',
    delete: CsConstant.PREFIX_CS + 'v1/bizIOrderHead',
    export: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/export',
    print: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/print',
    getIContractList: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/getIContractList',
    generateIOrder: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/generateIOrder',
    confirmIOrderHead: CsConstant.PREFIX_CS + 'v1/bizIOrderHead/confirmIOrderHead',
    getOrderHeadTotal:CsConstant.PREFIX_CS + 'v1/bizIOrderHead/getOrderHeadTotal',
    copyVersion: CsConstant.PREFIX_CS + '/v1/bizIOrderHead/copyVersion',
    checkOrderNoNotCancel: CsConstant.PREFIX_CS + '/v1/bizIOrderHead/checkOrderNoNotCancel',
    checkNextModuleExistEffectiveData: CsConstant.PREFIX_CS + '/v1/bizIOrderHead/checkNextModuleExistEffectiveData',
    cancelData: CsConstant.PREFIX_CS + '/v1/bizIOrderHead/cancelData',
    getOrderSupplierList:CsConstant.PREFIX_CS + '/v1/bizIOrderHead/getOrderSupplierList',
    rebootOrderNo:CsConstant.PREFIX_CS + '/v1/bizIOrderHead/rebootOrderNo',
    checkOrderHeadIsNextModule:CsConstant.PREFIX_CS + '/v1/bizIOrderHead/checkOrderHeadIsNextModule',
  },

  /*销售*/
  bizISellHead:{
    selectByheadId: CsConstant.PREFIX_CS + 'v1/BizISellHead/getSellHeadByOrderSid',
    update: CsConstant.PREFIX_CS + 'v1/BizISellHead',
    chargeback:CsConstant.PREFIX_CS + 'v1/BizISellHead/chargeback',
    confirm:CsConstant.PREFIX_CS + 'v1/BizISellHead/confirm',
    confirmReceipt:CsConstant.PREFIX_CS + 'v1/BizISellHead/confirmReceipt',
    confirmRefreshList:CsConstant.PREFIX_CS + 'v1/BizISellHead/confirmRefreshList',
  },
  bizISellList:{
    selectList: CsConstant.PREFIX_CS + 'v1/BizISellList/list',
    update: CsConstant.PREFIX_CS + 'v1/BizISellList',
    updateSellList: CsConstant.PREFIX_CS + 'v1/BizISellList/updateSellList',
    getSumDataByInvoice: CsConstant.PREFIX_CS + 'v1/BizISellList/getSumDataByInvoice',
    getSumDataByInvoiceSummary: CsConstant.PREFIX_CS + 'v1/BizISellList/getSumDataByInvoiceSummary',
  },
  /*入库*/
  bizIWarehouseReceiptHead:{
    selectByheadId: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead',
    abandonedData:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/abandonedData',
    generateBizIWarehouseReceipt:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/generateBizIWarehouseReceipt',
    checkPrintNotice:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/checkPrintNotice',

    printNotice:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/printNotice',
    onPrintOfLading:CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/printOfLading',
    print: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/print',
    onSure: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptHead/onSure',
  },
  bizIWarehouseReceiptList:{
    selectList: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList',
    getSumData: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList/getSumData',
    extractTaxes: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList/extractTaxes',
    getWarehouseReceiptListBySid: CsConstant.PREFIX_CS + 'v1/bizIWarehouseReceiptList/getWarehouseReceiptListBySid',
  },

  /*出库*/
  bizIReceiptHead:{
    selectByheadId: CsConstant.PREFIX_CS + 'v1/BizIReceiptHead/getReceiptHeadByOrderSid',
    update: CsConstant.PREFIX_CS + 'v1/BizIReceiptHead',
    export: CsConstant.PREFIX_CS + 'v1/BizIReceiptHead/export',
  },
  bizIReceiptList:{
    selectList: CsConstant.PREFIX_CS + 'v1/BizIReceiptList/list',
    update: CsConstant.PREFIX_CS + 'v1/BizIReceiptList',
    getSumDataByInvoiceSummary: CsConstant.PREFIX_CS + 'v1/BizIReceiptList/getSumDataByInvoiceSummary',
  },

  bizIOrderList: {
    list: CsConstant.PREFIX_CS + 'v1/bizIOrderList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIOrderList',
    insert: CsConstant.PREFIX_CS + 'v1/bizIOrderList',
    delete: CsConstant.PREFIX_CS + 'v1/bizIOrderList',
    export: CsConstant.PREFIX_CS + 'v1/bizIOrderList/export',
    getITotal: CsConstant.PREFIX_CS + 'v1/bizIOrderList/getITotal',
    getOrderListBySid: CsConstant.PREFIX_CS + 'v1/bizIOrderList/getOrderListBySid',
  },


  /* 进口进货信息  表头 */
  bizIPurchaseHead: {
    list: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead',
    insert: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead',
    delete: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead',
    export: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead/export',
    getPurchaseHeadByOrderSid: CsConstant.PREFIX_CS + 'v1/bizIPurchaseHead/getPurchaseHeadByOrderSid',
  },
  /* 进口进货信息  表体 */
  bizIPurchaseList: {
    list: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList',
    insert: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList',
    delete: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList',
    export: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/export',
    innerUpdatePurchaseList: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/innerUpdatePurchaseList',
    innerDecTotalUpdatePurchaseList: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/innerDecTotalUpdatePurchaseList',
    innerUpdatePurchaseListInvoiceNo: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/innerUpdatePurchaseListInvoiceNo',
    getSumData: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/getSumData',
    getSumDataByInvoice: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/getSumDataByInvoice',
    addPushListInvoice: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/addPushListInvoice',
    deletePurchaseList: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/deletePurchaseList',
    getPurchaseListBySid: CsConstant.PREFIX_CS + 'v1/bizIPurchaseList/getPurchaseListBySid',

  },

  /* 进口进货信息-装箱子表 */
  bizIPurchaseListBox: {
    list: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox',
    addPushListBox: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox/addPushListBox',
    getSumData: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox/getSumData',
    // 批量更新箱号
    batchUpdateBoxList: CsConstant.PREFIX_CS + 'v1/bizIPurchaseListBox/batchUpdateBoxList',
  },


  /* 进货管理 - 表头 */
  bizInComingHead: {
    list: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead',
    insert: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead',
    delete: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead',
    export: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/export',
    getSupplierList: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getSupplierList',
    getPortList: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getPortList',
    getCurrList: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getCurrList',
    getPriceTermList: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/getPriceTermList',
    confirmIncomingGoods: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsHead/confirmIncomingGoods',
  },

  /* 进货管理 - 表体 */
  bizInComingList: {
    list: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList',
    insert: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList',
    delete: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList',
    export: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsList/export',
  },

  /* 进货管理 - 证件信息 */
  bizIncomingGoodsDocument:{
    list: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument',
    insert: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument',
    delete: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument',
    export: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument/export',
    getDocumentByHeadId: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument/getDocumentByHeadId',
    insertOrUpdate: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsDocument/insertOrUpdate',
  },
  /* 进货管理- 投保信息 */
  bizIncomingGoodsInsurance:{
    list: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure/list',
    update: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure',
    insert: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure',
    delete: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure',
    export: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure/export',
    getDocumentByHeadId: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure/getDocumentByHeadId',
    insertOrUpdate: CsConstant.PREFIX_CS + 'v1/bizIncomingGoodsInsure/insertOrUpdate',
  },


  /* 进口单证信息 */
  bizIDocument: {
    list: CsConstant.PREFIX_CS + 'v1/bizIOrderDocument/list',
    insert: CsConstant.PREFIX_CS + 'v1/bizIOrderDocument',
  },

  pcodeUrl: {
    list: '/api/pcode'
  },
  fuYun:{
    downloadTemplate:'/api/fyImportBackend/v1/importConfig/download'
  },

  quoUrl:{
    bizQuotation : {
      list: CsConstant.PREFIX_CS +`/v1/bizQuotation/list`,
      insert: CsConstant.PREFIX_CS +`/v1/bizQuotation`,
      delete: CsConstant.PREFIX_CS +`/v1/bizQuotation`,
      cancel: CsConstant.PREFIX_CS +`/v1/bizQuotation/cancel`,
      enable: CsConstant.PREFIX_CS +`/v1/bizQuotation/enable`,
      update: CsConstant.PREFIX_CS +`/v1/bizQuotation`,
      getGNameList: CsConstant.PREFIX_CS +`/v1/bizQuotation/getGNameList`,
      export: CsConstant.PREFIX_CS +`/v1/bizQuotation/export`
    }
  }

}
